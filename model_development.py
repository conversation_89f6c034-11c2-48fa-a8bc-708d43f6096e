#!/usr/bin/env python3
"""
E-ticaret Session Value Prediction - Model Development
Farklı regresyon modellerini deneyerek en iyi performansı veren modeli seçmek
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.tree import DecisionTreeRegressor
from sklearn.svm import SVR
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

def load_processed_data():
    """İşlenmiş veri setlerini yükle"""
    print("İşlenmiş veri setleri yükleniyor...")

    X_train = pd.read_csv('X_train_features.csv')
    y_train = pd.read_csv('y_train.csv').squeeze()
    X_test = pd.read_csv('X_test_features.csv')
    test_sessions = pd.read_csv('test_sessions.csv')
    feature_names = pd.read_csv('feature_names.csv')

    print(f"Train features: {X_train.shape}")
    print(f"Train target: {y_train.shape}")
    print(f"Test features: {X_test.shape}")
    print(f"Feature count: {len(feature_names)}")

    return X_train, y_train, X_test, test_sessions, feature_names

def evaluate_model(model, X_train, y_train, X_val, y_val, model_name):
    """Model performansını değerlendir"""
    # Eğitim
    model.fit(X_train, y_train)

    # Tahminler
    train_pred = model.predict(X_train)
    val_pred = model.predict(X_val)

    # Metrikler
    train_mse = mean_squared_error(y_train, train_pred)
    val_mse = mean_squared_error(y_val, val_pred)
    train_mae = mean_absolute_error(y_train, train_pred)
    val_mae = mean_absolute_error(y_val, val_pred)
    train_r2 = r2_score(y_train, train_pred)
    val_r2 = r2_score(y_val, val_pred)

    results = {
        'model_name': model_name,
        'train_mse': train_mse,
        'val_mse': val_mse,
        'train_mae': train_mae,
        'val_mae': val_mae,
        'train_r2': train_r2,
        'val_r2': val_r2,
        'model': model
    }

    print(f"\n{model_name} Sonuçları:")
    print(f"Train MSE: {train_mse:.4f}, Val MSE: {val_mse:.4f}")
    print(f"Train MAE: {train_mae:.4f}, Val MAE: {val_mae:.4f}")
    print(f"Train R²: {train_r2:.4f}, Val R²: {val_r2:.4f}")

    return results

def train_baseline_models(X_train, y_train, X_val, y_val):
    """Baseline modelleri eğit"""
    print("\n" + "="*50)
    print("BASELINE MODELLER")
    print("="*50)

    models = {
        'Linear Regression': LinearRegression(),
        'Ridge Regression': Ridge(alpha=1.0),
        'Lasso Regression': Lasso(alpha=1.0),
        'Decision Tree': DecisionTreeRegressor(random_state=42, max_depth=10),
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
    }

    results = []

    for name, model in models.items():
        try:
            result = evaluate_model(model, X_train, y_train, X_val, y_val, name)
            results.append(result)
        except Exception as e:
            print(f"{name} modelinde hata: {e}")

    return results

def train_advanced_models(X_train, y_train, X_val, y_val):
    """Gelişmiş modelleri eğit"""
    print("\n" + "="*50)
    print("GELİŞMİŞ MODELLER")
    print("="*50)

    models = {
        'Gradient Boosting': GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42
        ),
        'XGBoost': xgb.XGBRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            eval_metric='rmse'
        ),
        'LightGBM': lgb.LGBMRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            verbose=-1
        )
    }

    results = []

    for name, model in models.items():
        try:
            result = evaluate_model(model, X_train, y_train, X_val, y_val, name)
            results.append(result)
        except Exception as e:
            print(f"{name} modelinde hata: {e}")

    return results

def cross_validate_best_models(X_train, y_train, best_models, cv_folds=5):
    """En iyi modelleri cross-validation ile değerlendir"""
    print("\n" + "="*50)
    print("CROSS-VALIDATION DEĞERLENDİRMESİ")
    print("="*50)

    cv_results = []

    for model_info in best_models:
        model = model_info['model']
        name = model_info['model_name']

        try:
            # Cross-validation MSE skorları (negatif olarak döner)
            cv_scores = cross_val_score(
                model, X_train, y_train,
                cv=cv_folds,
                scoring='neg_mean_squared_error'
            )

            # Pozitif MSE'ye çevir
            cv_mse_scores = -cv_scores

            cv_result = {
                'model_name': name,
                'cv_mse_mean': cv_mse_scores.mean(),
                'cv_mse_std': cv_mse_scores.std(),
                'cv_scores': cv_mse_scores,
                'model': model
            }

            cv_results.append(cv_result)

            print(f"{name}:")
            print(f"  CV MSE: {cv_mse_scores.mean():.4f} ± {cv_mse_scores.std():.4f}")

        except Exception as e:
            print(f"{name} CV değerlendirmesinde hata: {e}")

    return cv_results

def hyperparameter_tuning(X_train, y_train, best_model_info):
    """En iyi model için hiperparametre optimizasyonu"""
    print("\n" + "="*50)
    print("HİPERPARAMETRE OPTİMİZASYONU")
    print("="*50)

    model_name = best_model_info['model_name']
    print(f"Model: {model_name}")

    # Model tipine göre parametre grid'i belirle
    if 'XGBoost' in model_name:
        model = xgb.XGBRegressor(random_state=42, eval_metric='rmse')
        param_grid = {
            'n_estimators': [100, 200],
            'learning_rate': [0.05, 0.1, 0.2],
            'max_depth': [4, 6, 8],
            'subsample': [0.8, 1.0]
        }
    elif 'LightGBM' in model_name:
        model = lgb.LGBMRegressor(random_state=42, verbose=-1)
        param_grid = {
            'n_estimators': [100, 200],
            'learning_rate': [0.05, 0.1, 0.2],
            'max_depth': [4, 6, 8],
            'num_leaves': [31, 50, 100]
        }
    elif 'Random Forest' in model_name:
        model = RandomForestRegressor(random_state=42)
        param_grid = {
            'n_estimators': [100, 200],
            'max_depth': [6, 10, 15],
            'min_samples_split': [2, 5],
            'min_samples_leaf': [1, 2]
        }
    elif 'Gradient Boosting' in model_name:
        model = GradientBoostingRegressor(random_state=42)
        param_grid = {
            'n_estimators': [100, 200],
            'learning_rate': [0.05, 0.1, 0.2],
            'max_depth': [4, 6, 8],
            'subsample': [0.8, 1.0]
        }
    else:
        print(f"Hiperparametre tuning {model_name} için desteklenmiyor.")
        return best_model_info['model']

    # Grid Search
    print("Grid Search başlatılıyor...")
    grid_search = GridSearchCV(
        model,
        param_grid,
        cv=3,
        scoring='neg_mean_squared_error',
        n_jobs=-1,
        verbose=1
    )

    grid_search.fit(X_train, y_train)

    print(f"En iyi parametreler: {grid_search.best_params_}")
    print(f"En iyi CV MSE: {-grid_search.best_score_:.4f}")

    return grid_search.best_estimator_

def generate_predictions(model, X_test, test_sessions):
    """Test seti için tahminler üret"""
    print("\n" + "="*50)
    print("TAHMİN OLUŞTURMA")
    print("="*50)

    # Tahminler
    predictions = model.predict(X_test)

    # Negatif değerleri 0 ile değiştir (session value negatif olamaz)
    predictions = np.maximum(predictions, 0)

    # Submission formatında hazırla
    submission = pd.DataFrame({
        'user_session': test_sessions['user_session'],
        'session_value': predictions
    })

    print(f"Tahmin istatistikleri:")
    print(f"Min: {predictions.min():.4f}")
    print(f"Max: {predictions.max():.4f}")
    print(f"Ortalama: {predictions.mean():.4f}")
    print(f"Medyan: {np.median(predictions):.4f}")

    return submission

def save_model_results(all_results, cv_results, best_model, submission):
    """Model sonuçlarını kaydet"""
    print("\n" + "="*50)
    print("SONUÇLARI KAYDETME")
    print("="*50)

    # Model karşılaştırma tablosu
    results_df = pd.DataFrame(all_results)
    results_df.to_csv('model_comparison.csv', index=False)

    # CV sonuçları
    cv_df = pd.DataFrame([{
        'model_name': r['model_name'],
        'cv_mse_mean': r['cv_mse_mean'],
        'cv_mse_std': r['cv_mse_std']
    } for r in cv_results])
    cv_df.to_csv('cv_results.csv', index=False)

    # Submission dosyası
    submission.to_csv('submission.csv', index=False)

    print("Kaydedilen dosyalar:")
    print("- model_comparison.csv")
    print("- cv_results.csv")
    print("- submission.csv")

def main():
    """Ana fonksiyon"""
    print("E-ticaret Session Value Prediction - Model Development")
    print("="*60)

    # Veri yükleme
    X_train, y_train, X_test, test_sessions, feature_names = load_processed_data()

    # Train-validation split
    X_train_split, X_val, y_train_split, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )

    print(f"\nTrain split: {X_train_split.shape}")
    print(f"Validation split: {X_val.shape}")

    # Baseline modeller
    baseline_results = train_baseline_models(X_train_split, y_train_split, X_val, y_val)

    # Gelişmiş modeller
    advanced_results = train_advanced_models(X_train_split, y_train_split, X_val, y_val)

    # Tüm sonuçları birleştir
    all_results = baseline_results + advanced_results

    # En iyi 3 modeli seç (validation MSE'ye göre)
    all_results.sort(key=lambda x: x['val_mse'])
    best_models = all_results[:3]

    print(f"\nEn iyi 3 model:")
    for i, model in enumerate(best_models, 1):
        print(f"{i}. {model['model_name']}: Val MSE = {model['val_mse']:.4f}")

    # Cross-validation
    cv_results = cross_validate_best_models(X_train, y_train, best_models)

    # En iyi modeli seç (CV MSE'ye göre)
    cv_results.sort(key=lambda x: x['cv_mse_mean'])
    best_model_info = cv_results[0]

    print(f"\nEn iyi model: {best_model_info['model_name']}")
    print(f"CV MSE: {best_model_info['cv_mse_mean']:.4f} ± {best_model_info['cv_mse_std']:.4f}")

    # Hiperparametre tuning
    tuned_model = hyperparameter_tuning(X_train, y_train, best_model_info)

    # Final model eğitimi (tüm train verisi ile)
    print("\nFinal model eğitiliyor...")
    tuned_model.fit(X_train, y_train)

    # Test tahminleri
    submission = generate_predictions(tuned_model, X_test, test_sessions)

    # Sonuçları kaydet
    save_model_results(all_results, cv_results, tuned_model, submission)

    print(f"\nModel development tamamlandı!")
    print(f"En iyi model: {best_model_info['model_name']}")
    print(f"Submission dosyası hazır: submission.csv")

    return tuned_model, submission

if __name__ == "__main__":
    main()