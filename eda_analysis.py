#!/usr/bin/env python3
"""
E-ticaret Session Value Prediction - EDA Analysis
Kaggle yarışması için veri keşfi ve analizi
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import dtale
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Veri setlerini yükle"""
    print("Veri setleri yükleniyor...")

    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_submission = pd.read_csv('sample_submission.csv')

    print(f"Train veri seti boyutu: {train_df.shape}")
    print(f"Test veri seti boyutu: {test_df.shape}")
    print(f"Sample submission boyutu: {sample_submission.shape}")

    return train_df, test_df, sample_submission

def basic_info_analysis(train_df, test_df):
    """Temel veri analizi"""
    print("\n" + "="*50)
    print("TEMEL VERİ ANALİZİ")
    print("="*50)

    print("\nTrain veri seti bilgileri:")
    print(train_df.info())
    print("\nTrain veri seti istatistikleri:")
    print(train_df.describe())

    print("\nTest veri seti bilgileri:")
    print(test_df.info())

    # Eksik değer kontrolü
    print("\nTrain veri setinde eksik değerler:")
    print(train_df.isnull().sum())

    print("\nTest veri setinde eksik değerler:")
    print(test_df.isnull().sum())

    # Benzersiz değer sayıları
    print("\nTrain veri setinde benzersiz değer sayıları:")
    for col in train_df.columns:
        print(f"{col}: {train_df[col].nunique()}")

    print("\nTest veri setinde benzersiz değer sayıları:")
    for col in test_df.columns:
        print(f"{col}: {test_df[col].nunique()}")

def event_type_analysis(train_df, test_df):
    """Event type analizi"""
    print("\n" + "="*50)
    print("EVENT TYPE ANALİZİ")
    print("="*50)

    print("\nTrain veri setinde event type dağılımı:")
    train_event_counts = train_df['event_type'].value_counts()
    print(train_event_counts)
    print(f"\nTrain event type oranları:")
    print(train_event_counts / len(train_df) * 100)

    print("\nTest veri setinde event type dağılımı:")
    test_event_counts = test_df['event_type'].value_counts()
    print(test_event_counts)
    print(f"\nTest event type oranları:")
    print(test_event_counts / len(test_df) * 100)

def session_analysis(train_df, test_df):
    """Session bazlı analiz"""
    print("\n" + "="*50)
    print("SESSION ANALİZİ")
    print("="*50)

    # Train session analizi
    train_session_stats = train_df.groupby('user_session').agg({
        'event_type': 'count',
        'product_id': 'nunique',
        'category_id': 'nunique',
        'session_value': 'first'
    }).rename(columns={
        'event_type': 'event_count',
        'product_id': 'unique_products',
        'category_id': 'unique_categories'
    })

    print("Train session istatistikleri:")
    print(train_session_stats.describe())

    # Test session analizi
    test_session_stats = test_df.groupby('user_session').agg({
        'event_type': 'count',
        'product_id': 'nunique',
        'category_id': 'nunique'
    }).rename(columns={
        'event_type': 'event_count',
        'product_id': 'unique_products',
        'category_id': 'unique_categories'
    })

    print("\nTest session istatistikleri:")
    print(test_session_stats.describe())

    return train_session_stats, test_session_stats

def session_value_analysis(train_df):
    """Session value analizi"""
    print("\n" + "="*50)
    print("SESSION VALUE ANALİZİ")
    print("="*50)

    print("Session value istatistikleri:")
    print(train_df['session_value'].describe())

    print(f"\nSession value dağılımı:")
    print(f"Min: {train_df['session_value'].min()}")
    print(f"Max: {train_df['session_value'].max()}")
    print(f"Ortalama: {train_df['session_value'].mean():.2f}")
    print(f"Medyan: {train_df['session_value'].median():.2f}")
    print(f"Standart sapma: {train_df['session_value'].std():.2f}")

def time_analysis(train_df, test_df):
    """Zaman bazlı analiz"""
    print("\n" + "="*50)
    print("ZAMAN ANALİZİ")
    print("="*50)

    # Datetime dönüşümü
    train_df['event_time'] = pd.to_datetime(train_df['event_time'])
    test_df['event_time'] = pd.to_datetime(test_df['event_time'])

    # Zaman özellikleri çıkar
    train_df['hour'] = train_df['event_time'].dt.hour
    train_df['day_of_week'] = train_df['event_time'].dt.dayofweek
    train_df['day'] = train_df['event_time'].dt.day

    test_df['hour'] = test_df['event_time'].dt.hour
    test_df['day_of_week'] = test_df['event_time'].dt.dayofweek
    test_df['day'] = test_df['event_time'].dt.day

    print("Train veri seti zaman aralığı:")
    print(f"Başlangıç: {train_df['event_time'].min()}")
    print(f"Bitiş: {train_df['event_time'].max()}")

    print("\nTest veri seti zaman aralığı:")
    print(f"Başlangıç: {test_df['event_time'].min()}")
    print(f"Bitiş: {test_df['event_time'].max()}")

def launch_dtale_analysis(train_df, test_df):
    """D-Tale ile interaktif analiz başlat"""
    print("\n" + "="*50)
    print("D-TALE İNTERAKTİF ANALİZ")
    print("="*50)

    print("D-Tale ile train veri seti analizi başlatılıyor...")

    # Session bazlı özet veri hazırla
    train_session_summary = train_df.groupby('user_session').agg({
        'event_type': ['count', lambda x: x.value_counts().to_dict()],
        'product_id': 'nunique',
        'category_id': 'nunique',
        'user_id': 'first',
        'session_value': 'first',
        'event_time': ['min', 'max']
    }).reset_index()

    # Kolon isimlerini düzelt
    train_session_summary.columns = [
        'user_session', 'total_events', 'event_type_dist',
        'unique_products', 'unique_categories', 'user_id',
        'session_value', 'session_start', 'session_end'
    ]

    # D-Tale başlat
    d = dtale.show(train_session_summary, host='localhost', port=40000)
    print(f"D-Tale başlatıldı: {d._main_url}")
    print("Tarayıcınızda açılacak. Kapatmak için Ctrl+C kullanın.")

    return d, train_session_summary

def main():
    """Ana fonksiyon"""
    print("E-ticaret Session Value Prediction - EDA Analizi")
    print("="*60)

    # Veri yükleme
    train_df, test_df, sample_submission = load_data()

    # Temel analiz
    basic_info_analysis(train_df, test_df)

    # Event type analizi
    event_type_analysis(train_df, test_df)

    # Session analizi
    train_session_stats, test_session_stats = session_analysis(train_df, test_df)

    # Session value analizi
    session_value_analysis(train_df)

    # Zaman analizi
    time_analysis(train_df, test_df)

    # D-Tale analizi
    print("\nD-Tale analizi başlatmak istiyor musunuz? (y/n): ", end="")
    choice = input().lower()

    if choice == 'y':
        d, train_session_summary = launch_dtale_analysis(train_df, test_df)

        # Veriyi kaydet
        train_session_summary.to_csv('train_session_summary.csv', index=False)
        print("\nSession özet verisi 'train_session_summary.csv' olarak kaydedildi.")

        return d, train_session_summary, train_df, test_df
    else:
        return None, None, train_df, test_df

if __name__ == "__main__":
    main()