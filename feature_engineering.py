#!/usr/bin/env python3
"""
E-ticaret Session Value Prediction - Feature Engineering
EDA bulgularına dayanarak özellik çıkarımı
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data():
    """Veri setlerini yükle ve temel hazırlık yap"""
    print("Veri setleri yükleniyor...")

    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_submission = pd.read_csv('sample_submission.csv')

    # Datetime dönüşümü
    train_df['event_time'] = pd.to_datetime(train_df['event_time'])
    test_df['event_time'] = pd.to_datetime(test_df['event_time'])

    print(f"Train: {train_df.shape}, Test: {test_df.shape}")
    return train_df, test_df, sample_submission

def extract_time_features(df):
    """Zaman bazlı özellikler çıkar"""
    print("Zaman özellikleri çıkarılıyor...")

    df = df.copy()

    # Temel zaman özellikleri
    df['hour'] = df['event_time'].dt.hour
    df['day_of_week'] = df['event_time'].dt.dayofweek
    df['day'] = df['event_time'].dt.day
    df['minute'] = df['event_time'].dt.minute

    # Zaman kategorileri
    df['time_of_day'] = pd.cut(df['hour'],
                              bins=[0, 6, 12, 18, 24],
                              labels=['night', 'morning', 'afternoon', 'evening'],
                              include_lowest=True)

    # Hafta içi/sonu
    df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)

    return df

def create_session_features(df):
    """Session bazlı özellikler oluştur"""
    print("Session özellikleri oluşturuluyor...")

    # Session bazlı agregasyonlar
    session_features = df.groupby('user_session').agg({
        'event_type': ['count', 'nunique'],
        'product_id': 'nunique',
        'category_id': 'nunique',
        'user_id': 'first',
        'hour': ['min', 'max', 'mean'],
        'day_of_week': 'first',
        'is_weekend': 'first',
        'event_time': ['min', 'max']
    }).reset_index()

    # Kolon isimlerini düzelt
    session_features.columns = [
        'user_session', 'total_events', 'unique_event_types',
        'unique_products', 'unique_categories', 'user_id',
        'hour_min', 'hour_max', 'hour_mean',
        'day_of_week', 'is_weekend', 'session_start', 'session_end'
    ]

    # Session süresi (dakika)
    session_features['session_duration_minutes'] = (
        session_features['session_end'] - session_features['session_start']
    ).dt.total_seconds() / 60

    # Saat aralığı
    session_features['hour_range'] = session_features['hour_max'] - session_features['hour_min']

    return session_features

def create_event_type_features(df):
    """Event type bazlı özellikler oluştur"""
    print("Event type özellikleri oluşturuluyor...")

    # Event type pivot tablosu
    event_pivot = df.groupby(['user_session', 'event_type']).size().unstack(fill_value=0)
    event_pivot = event_pivot.reset_index()

    # Kolon isimlerini düzelt
    event_columns = {col: f'event_{col.lower()}_count' for col in event_pivot.columns if col != 'user_session'}
    event_pivot = event_pivot.rename(columns=event_columns)

    # Event oranları hesapla
    total_events = event_pivot[[col for col in event_pivot.columns if 'count' in col]].sum(axis=1)

    for col in event_pivot.columns:
        if 'count' in col:
            ratio_col = col.replace('count', 'ratio')
            event_pivot[ratio_col] = event_pivot[col] / (total_events + 1e-8)

    # Conversion rate (BUY / total events)
    if 'event_buy_count' in event_pivot.columns:
        event_pivot['conversion_rate'] = event_pivot['event_buy_count'] / (total_events + 1e-8)
    else:
        event_pivot['conversion_rate'] = 0

    # Cart abandonment rate (ADD_CART - BUY) / ADD_CART
    if 'event_add_cart_count' in event_pivot.columns and 'event_buy_count' in event_pivot.columns:
        event_pivot['cart_abandonment_rate'] = (
            (event_pivot['event_add_cart_count'] - event_pivot['event_buy_count']) /
            (event_pivot['event_add_cart_count'] + 1e-8)
        )
    else:
        event_pivot['cart_abandonment_rate'] = 0

    return event_pivot

def create_product_category_features(df):
    """Ürün ve kategori bazlı özellikler oluştur"""
    print("Ürün ve kategori özellikleri oluşturuluyor...")

    session_features = df.groupby('user_session').agg({
        'product_id': ['nunique', 'count'],
        'category_id': ['nunique', 'count']
    }).reset_index()

    # Kolon isimlerini düzelt
    session_features.columns = [
        'user_session', 'unique_products', 'total_product_interactions',
        'unique_categories', 'total_category_interactions'
    ]

    # Ürün çeşitliliği oranı
    session_features['product_diversity_ratio'] = (
        session_features['unique_products'] / (session_features['total_product_interactions'] + 1e-8)
    )

    # Kategori çeşitliliği oranı
    session_features['category_diversity_ratio'] = (
        session_features['unique_categories'] / (session_features['total_category_interactions'] + 1e-8)
    )

    return session_features

def create_user_features(df):
    """Kullanıcı bazlı özellikler oluştur"""
    print("Kullanıcı özellikleri oluşturuluyor...")

    # Kullanıcı bazlı agregasyonlar
    user_features = df.groupby('user_id').agg({
        'user_session': 'nunique',
        'event_type': 'count',
        'product_id': 'nunique',
        'category_id': 'nunique'
    }).reset_index()

    user_features.columns = [
        'user_id', 'user_total_sessions', 'user_total_events',
        'user_unique_products', 'user_unique_categories'
    ]

    # Kullanıcı aktivite oranları
    user_features['user_events_per_session'] = (
        user_features['user_total_events'] / (user_features['user_total_sessions'] + 1e-8)
    )

    return user_features

def encode_categorical_features(train_features, test_features):
    """Kategorik özellikleri encode et"""
    print("Kategorik özellikler encode ediliyor...")

    categorical_columns = ['user_id', 'day_of_week']

    # Label encoding
    label_encoders = {}

    for col in categorical_columns:
        if col in train_features.columns:
            le = LabelEncoder()

            # Train ve test'i birleştir, fit et, sonra ayır
            combined_values = pd.concat([
                train_features[col].astype(str),
                test_features[col].astype(str)
            ]).unique()

            le.fit(combined_values)

            train_features[col + '_encoded'] = le.transform(train_features[col].astype(str))
            test_features[col + '_encoded'] = le.transform(test_features[col].astype(str))

            label_encoders[col] = le

    return train_features, test_features, label_encoders

def combine_all_features(train_df, test_df):
    """Tüm özellikleri birleştir"""
    print("Tüm özellikler birleştiriliyor...")

    # Zaman özellikleri ekle
    train_df = extract_time_features(train_df)
    test_df = extract_time_features(test_df)

    # Session özellikleri
    train_session_features = create_session_features(train_df)
    test_session_features = create_session_features(test_df)

    # Event type özellikleri
    train_event_features = create_event_type_features(train_df)
    test_event_features = create_event_type_features(test_df)

    # Ürün/kategori özellikleri
    train_product_features = create_product_category_features(train_df)
    test_product_features = create_product_category_features(test_df)

    # Kullanıcı özellikleri
    train_user_features = create_user_features(train_df)
    test_user_features = create_user_features(test_df)

    # Session bazlı birleştirme
    train_features = train_session_features.merge(train_event_features, on='user_session', how='left')
    train_features = train_features.merge(train_product_features, on='user_session', how='left')
    train_features = train_features.merge(train_user_features, on='user_id', how='left')

    test_features = test_session_features.merge(test_event_features, on='user_session', how='left')
    test_features = test_features.merge(test_product_features, on='user_session', how='left')
    test_features = test_features.merge(test_user_features, on='user_id', how='left')

    # Target değişkeni ekle (sadece train için)
    if 'session_value' in train_df.columns:
        session_targets = train_df.groupby('user_session')['session_value'].first().reset_index()
        train_features = train_features.merge(session_targets, on='user_session', how='left')

    # Eksik değerleri doldur
    train_features = train_features.fillna(0)
    test_features = test_features.fillna(0)

    # Kategorik encoding
    train_features, test_features, label_encoders = encode_categorical_features(train_features, test_features)

    return train_features, test_features, label_encoders

def prepare_final_datasets(train_features, test_features):
    """Final veri setlerini hazırla"""
    print("Final veri setleri hazırlanıyor...")

    # String sütunları çıkar ve encode edilmiş versiyonları kullan
    exclude_columns = ['user_session', 'session_value', 'session_start', 'session_end', 'user_id']
    feature_columns = [col for col in train_features.columns if col not in exclude_columns]

    X_train = train_features[feature_columns]
    y_train = train_features['session_value']
    X_test = test_features[feature_columns]

    # Test setinde eksik olan sütunları train'den ekle (0 ile doldur)
    missing_cols = set(X_train.columns) - set(X_test.columns)
    for col in missing_cols:
        X_test[col] = 0

    # Train setinde eksik olan sütunları test'ten ekle (0 ile doldur)
    missing_cols = set(X_test.columns) - set(X_train.columns)
    for col in missing_cols:
        X_train[col] = 0

    # Sütun sırasını aynı yap
    X_test = X_test[X_train.columns]

    print(f"Final train shape: {X_train.shape}")
    print(f"Final test shape: {X_test.shape}")
    print(f"Target shape: {y_train.shape}")

    return X_train, y_train, X_test, test_features[['user_session']]

def main():
    """Ana fonksiyon"""
    print("E-ticaret Session Value Prediction - Feature Engineering")
    print("="*60)

    # Veri yükleme
    train_df, test_df, sample_submission = load_and_prepare_data()

    # Özellik mühendisliği
    train_features, test_features, label_encoders = combine_all_features(train_df, test_df)

    # Final veri setleri
    X_train, y_train, X_test, test_sessions = prepare_final_datasets(train_features, test_features)

    # Veri setlerini kaydet
    print("Veri setleri kaydediliyor...")
    X_train.to_csv('X_train_features.csv', index=False)
    y_train.to_csv('y_train.csv', index=False)
    X_test.to_csv('X_test_features.csv', index=False)
    test_sessions.to_csv('test_sessions.csv', index=False)

    # Özellik isimlerini kaydet
    feature_names = pd.DataFrame({'feature_names': X_train.columns})
    feature_names.to_csv('feature_names.csv', index=False)

    print("Feature engineering tamamlandı!")
    print(f"Toplam özellik sayısı: {len(X_train.columns)}")
    print("Kaydedilen dosyalar:")
    print("- X_train_features.csv")
    print("- y_train.csv")
    print("- X_test_features.csv")
    print("- test_sessions.csv")
    print("- feature_names.csv")

    return X_train, y_train, X_test, test_sessions

if __name__ == "__main__":
    main()