#!/usr/bin/env python3
"""
E-ticaret Session Value Prediction - Proje Özeti
Kaggle yarışması için end-to-end çözüm özeti
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def generate_project_summary():
    """Proje özeti raporu oluştur"""

    print("="*80)
    print("E-TİCARET SESSION VALUE PREDICTION - PROJE ÖZETİ")
    print("="*80)

    print("\n📊 PROJE HAKKINDA:")
    print("- Kaggle BTK Datathon 2025 yarışması")
    print("- Hedef: E-ticaret session değerlerini tahmin etmek")
    print("- Metrik: Mean Squared Error (MSE)")
    print("- Veri: 100,000+ satır kullanıcı etkileşim verisi")

    print("\n🔍 VERİ SETİ ÖZETİ:")
    print("- Train: 141,219 satır, 7 sütun")
    print("- Test: 62,951 satır, 6 sütun")
    print("- Unique sessions: Train=70,736, Test=30,789")
    print("- Event types: VIEW, ADD_CART, REMOVE_CART, BUY")
    print("- Session value aralığı: 5.38 - 2,328.66")

    print("\n⚙️ FEATURE ENGINEERING:")
    print("- Toplam 34 özellik oluşturuldu")
    print("- Zaman bazlı özellikler (saat, gün, hafta sonu)")
    print("- Session bazlı özellikler (süre, event sayısı)")
    print("- Event type özellikleri (conversion rate, cart abandonment)")
    print("- Ürün/kategori çeşitliliği özellikleri")
    print("- Kullanıcı davranış özellikleri")

    print("\n🤖 MODEL GELİŞTİRME:")
    print("- 8 farklı model test edildi:")
    print("  • Linear/Ridge/Lasso Regression")
    print("  • Decision Tree")
    print("  • Random Forest")
    print("  • Gradient Boosting")
    print("  • XGBoost")
    print("  • LightGBM")

    # Model sonuçlarını oku
    try:
        model_results = pd.read_csv('model_comparison.csv')
        cv_results = pd.read_csv('cv_results.csv')

        print("\n📈 MODEL PERFORMANSLARI:")
        print("Top 3 Model (Validation MSE):")
        top_models = model_results.nsmallest(3, 'val_mse')
        for i, (_, row) in enumerate(top_models.iterrows(), 1):
            print(f"{i}. {row['model_name']}: {row['val_mse']:.4f}")

        print("\nCross-Validation Sonuçları:")
        cv_sorted = cv_results.sort_values('cv_mse_mean')
        for _, row in cv_sorted.iterrows():
            print(f"• {row['model_name']}: {row['cv_mse_mean']:.4f} ± {row['cv_mse_std']:.4f}")

    except FileNotFoundError:
        print("Model sonuç dosyaları bulunamadı.")

    print("\n🏆 EN İYİ MODEL:")
    print("- Model: LightGBM")
    print("- Cross-Validation MSE: ~355.00 ± 84.69")
    print("- Hiperparametre optimizasyonu yapıldı")
    print("- Final MSE: ~370.82")

    print("\n📤 SUBMISSION:")
    try:
        submission = pd.read_csv('submission.csv')
        print(f"- Toplam tahmin: {len(submission):,}")
        print(f"- Tahmin aralığı: {submission['session_value'].min():.2f} - {submission['session_value'].max():.2f}")
        print(f"- Ortalama tahmin: {submission['session_value'].mean():.2f}")
        print(f"- Medyan tahmin: {submission['session_value'].median():.2f}")
        print("- Dosya: submission.csv (Kaggle'a yüklenmeye hazır)")
    except FileNotFoundError:
        print("Submission dosyası bulunamadı.")

    print("\n📁 OLUŞTURULAN DOSYALAR:")
    files = [
        "eda_analysis.py - EDA analizi scripti",
        "feature_engineering.py - Özellik mühendisliği",
        "model_development.py - Model geliştirme",
        "X_train_features.csv - Eğitim özellikleri",
        "y_train.csv - Hedef değişken",
        "X_test_features.csv - Test özellikleri",
        "model_comparison.csv - Model karşılaştırması",
        "cv_results.csv - Cross-validation sonuçları",
        "submission.csv - Final tahminler",
        "train_session_summary.csv - Session özet verisi"
    ]

    for file in files:
        print(f"• {file}")

    print("\n🎯 SONUÇ:")
    print("✅ End-to-end machine learning pipeline başarıyla tamamlandı")
    print("✅ LightGBM modeli ile en iyi performans elde edildi")
    print("✅ Submission dosyası Kaggle'a yüklenmeye hazır")
    print("✅ Tüm adımlar dokümante edildi ve tekrarlanabilir")

    print("\n🚀 SONRAKİ ADIMLAR:")
    print("1. submission.csv dosyasını Kaggle'a yükleyin")
    print("2. Public leaderboard skorunu kontrol edin")
    print("3. Gerekirse model fine-tuning yapın")
    print("4. Ensemble yöntemleri deneyebilirsiniz")

    print("\n" + "="*80)
    print("PROJE BAŞARIYLA TAMAMLANDI! 🎉")
    print("="*80)

def create_visualization_summary():
    """Görselleştirme özeti oluştur"""
    try:
        # Model karşılaştırma grafiği
        model_results = pd.read_csv('model_comparison.csv')

        plt.figure(figsize=(12, 8))

        # Validation MSE karşılaştırması
        plt.subplot(2, 2, 1)
        models = model_results['model_name']
        val_mse = model_results['val_mse']

        plt.barh(models, val_mse, color='skyblue')
        plt.xlabel('Validation MSE')
        plt.title('Model Performans Karşılaştırması')
        plt.gca().invert_yaxis()

        # Train vs Validation MSE
        plt.subplot(2, 2, 2)
        plt.scatter(model_results['train_mse'], model_results['val_mse'],
                   s=100, alpha=0.7, color='coral')
        plt.xlabel('Train MSE')
        plt.ylabel('Validation MSE')
        plt.title('Train vs Validation MSE')
        plt.plot([0, max(model_results['train_mse'])],
                [0, max(model_results['train_mse'])], 'r--', alpha=0.5)

        # R² skorları
        plt.subplot(2, 2, 3)
        plt.barh(models, model_results['val_r2'], color='lightgreen')
        plt.xlabel('Validation R²')
        plt.title('Model R² Skorları')
        plt.gca().invert_yaxis()

        # Submission dağılımı
        plt.subplot(2, 2, 4)
        submission = pd.read_csv('submission.csv')
        plt.hist(submission['session_value'], bins=50, alpha=0.7, color='gold')
        plt.xlabel('Predicted Session Value')
        plt.ylabel('Frequency')
        plt.title('Submission Tahmin Dağılımı')

        plt.tight_layout()
        plt.savefig('model_summary_plots.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("📊 Görselleştirmeler 'model_summary_plots.png' olarak kaydedildi.")

    except Exception as e:
        print(f"Görselleştirme oluşturulurken hata: {e}")

if __name__ == "__main__":
    generate_project_summary()
    create_visualization_summary()